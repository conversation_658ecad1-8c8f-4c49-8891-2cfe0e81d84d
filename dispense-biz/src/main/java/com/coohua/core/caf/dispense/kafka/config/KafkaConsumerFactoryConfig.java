package com.coohua.core.caf.dispense.kafka.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Configuration
@EnableKafka
public class KafkaConsumerFactoryConfig {

    @Bean("userActiveConsumerFactory")
    public ConsumerFactory<Object, Object> userActiveConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();

        // 基础配置
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-pre-cn-tl32lya4k00m-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-tl32lya4k00m-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-tl32lya4k00m-3-vpc.alikafka.aliyuncs.com:9092");
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, "dispense_user_active_login");
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

        // 序列化配置
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        // 消费配置
        configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 500);
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false); // 手动提交
        configProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
        configProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10000);
        configProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 300000); // 5分钟

        // 性能优化配置
        configProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024 * 5);
        configProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 50);

        log.info("Kafka消费者配置初始化完成: groupId={}, servers=k00m", "dispense_user_active_login");

        return new DefaultKafkaConsumerFactory<>(configProps);
    }

    @Bean("userActiveListenerFactory")
    public ConcurrentKafkaListenerContainerFactory<Object, Object> userActiveListenerFactory() {
        ConcurrentKafkaListenerContainerFactory<Object, Object> factory =
                new ConcurrentKafkaListenerContainerFactory<>();

        factory.setConsumerFactory(userActiveConsumerFactory());
        factory.setConcurrency(2);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.setErrorHandler((exception, data) -> {
            log.error("Kafka消费异常: data={}", data, exception);
        });
        log.info("Kafka消费者listener配置初始化完成: groupId={}, servers=k00m", "dispense_user_active_login");
        return factory;
    }
}
